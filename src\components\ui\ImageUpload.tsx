import React, { useState, useRef } from "react";

interface ImageUploadProps {
  onImagesUploaded: (imageUrls: string[]) => void;
  donationId?: string;
  maxImages?: number;
  className?: string;
}

interface UploadedImage {
  url: string;
  file: File;
  uploading: boolean;
  error?: string;
  previewUrl?: string; // For local preview before upload
}

export default function ImageUpload({
  onImagesUploaded,
  donationId,
  maxImages = 5,
  className = "",
}: ImageUploadProps) {
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(event.target.files || []);

    if (files.length === 0) return;

    // Check if adding these files would exceed the limit
    if (images.length + files.length > maxImages) {
      alert(`You can only upload up to ${maxImages} images.`);
      return;
    }

    // Validate files
    const validFiles = files.filter((file) => {
      const isValidType = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/webp",
      ].includes(file.type);
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB

      if (!isValidType) {
        alert(
          `File "${file.name}" is not a valid image type. Only JPEG, PNG, and WebP are allowed.`
        );
        return false;
      }

      if (!isValidSize) {
        alert(`File "${file.name}" is too large. Maximum size is 5MB.`);
        return false;
      }

      return true;
    });

    if (validFiles.length === 0) return;

    // Add files to state with uploading status
    const newImages: UploadedImage[] = validFiles.map((file) => {
      const previewUrl = URL.createObjectURL(file);
      return {
        url: previewUrl,
        previewUrl,
        file,
        uploading: true,
      };
    });

    setImages((prev) => [...prev, ...newImages]);
    setUploading(true);

    // Upload files
    const uploadPromises = validFiles.map(async (file, index) => {
      try {
        const formData = new FormData();
        formData.append("image", file);
        if (donationId) {
          formData.append("donationId", donationId);
        }

        const response = await fetch("/api/uploads/images", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Upload failed");
        }

        const data = await response.json();
        console.log("Upload response data:", data);

        // Update the image with the actual URL
        setImages((prev) => {
          const updated = [...prev];
          const imageIndex = prev.length - validFiles.length + index;
          console.log(
            "Updating image at index",
            imageIndex,
            "with URL:",
            data.imageUrl
          );
          updated[imageIndex] = {
            ...updated[imageIndex],
            url: data.imageUrl,
            uploading: false,
          };
          return updated;
        });

        return data.imageUrl;
      } catch (error) {
        console.error("Upload error:", error);

        // Mark image as failed
        setImages((prev) => {
          const updated = [...prev];
          const imageIndex = prev.length - validFiles.length + index;
          updated[imageIndex] = {
            ...updated[imageIndex],
            uploading: false,
            error: error instanceof Error ? error.message : "Upload failed",
          };
          return updated;
        });

        return null;
      }
    });

    try {
      const uploadedUrls = await Promise.all(uploadPromises);
      const successfulUrls = uploadedUrls.filter(
        (url) => url !== null
      ) as string[];

      // Get all successfully uploaded URLs (proxy URLs from our API)
      const allSuccessfulUrls = images
        .filter(
          (img) =>
            !img.uploading &&
            !img.error &&
            (img.url.startsWith("http") || img.url.startsWith("/api/images/"))
        )
        .map((img) => img.url)
        .concat(successfulUrls);

      onImagesUploaded(allSuccessfulUrls);
    } catch (error) {
      console.error("Error during uploads:", error);
    } finally {
      setUploading(false);
    }

    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeImage = (indexToRemove: number) => {
    setImages((prev) => {
      const updated = prev.filter((_, index) => index !== indexToRemove);

      // Update the parent with remaining URLs
      const remainingUrls = updated
        .filter(
          (img) =>
            !img.uploading &&
            !img.error &&
            (img.url.startsWith("http") || img.url.startsWith("/api/images/"))
        )
        .map((img) => img.url);

      onImagesUploaded(remainingUrls);

      return updated;
    });
  };

  const retryUpload = async (indexToRetry: number) => {
    const imageToRetry = images[indexToRetry];
    if (!imageToRetry || !imageToRetry.error) return;

    setImages((prev) => {
      const updated = [...prev];
      updated[indexToRetry] = {
        ...updated[indexToRetry],
        uploading: true,
        error: undefined,
      };
      return updated;
    });

    try {
      const formData = new FormData();
      formData.append("image", imageToRetry.file);
      if (donationId) {
        formData.append("donationId", donationId);
      }

      const response = await fetch("/api/uploads/images", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      const data = await response.json();

      setImages((prev) => {
        const updated = [...prev];
        updated[indexToRetry] = {
          ...updated[indexToRetry],
          url: data.imageUrl,
          uploading: false,
        };
        return updated;
      });

      // Update parent with all successful URLs
      const allSuccessfulUrls = images
        .map((img, index) => (index === indexToRetry ? data.imageUrl : img.url))
        .filter(
          (url, index) =>
            !images[index].uploading &&
            !images[index].error &&
            (url.startsWith("http") || url.startsWith("/api/images/"))
        );

      onImagesUploaded(allSuccessfulUrls);
    } catch (error) {
      setImages((prev) => {
        const updated = [...prev];
        updated[indexToRetry] = {
          ...updated[indexToRetry],
          uploading: false,
          error: error instanceof Error ? error.message : "Upload failed",
        };
        return updated;
      });
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Button */}
      <div className="flex items-center gap-4">
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading || images.length >= maxImages}
          className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          {uploading ? "Uploading..." : "Add Images"}
        </button>
        <span className="text-sm text-gray-600">
          {images.length}/{maxImages} images uploaded
        </span>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/webp"
          multiple
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <div
              key={index}
              className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden border"
            >
              <img
                src={image.url}
                alt={`Upload ${index + 1}`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error("Image failed to load:", image.url);
                  const target = e.target as HTMLImageElement;
                  // If proxy URL fails and we have a preview URL, try that
                  if (
                    image.previewUrl &&
                    image.url !== image.previewUrl &&
                    !image.uploading
                  ) {
                    console.log(
                      "Proxy URL failed, falling back to preview URL:",
                      image.previewUrl
                    );
                    target.src = image.previewUrl;
                  } else {
                    // Show error state
                    target.style.display = "none";
                    const parent = target.parentNode as HTMLElement;
                    if (parent && !parent.querySelector(".fallback-error")) {
                      const fallback = document.createElement("div");
                      fallback.className =
                        "fallback-error w-full h-full flex items-center justify-center bg-gray-200 text-gray-500 text-xs";
                      fallback.textContent = image.uploading
                        ? "Uploading..."
                        : "Image failed to load";
                      parent.appendChild(fallback);
                    }
                  }
                }}
                onLoad={() => {
                  console.log("Image loaded successfully:", image.url);
                }}
              />

              {/* Loading Overlay */}
              {image.uploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="text-white text-sm">Uploading...</div>
                </div>
              )}

              {/* Error Overlay */}
              {image.error && (
                <div className="absolute inset-0 bg-red-500 bg-opacity-80 flex flex-col items-center justify-center p-2">
                  <div className="text-white text-xs text-center mb-2">
                    {image.error}
                  </div>
                  <button
                    onClick={() => retryUpload(index)}
                    className="bg-white text-red-500 px-2 py-1 rounded text-xs font-medium"
                  >
                    Retry
                  </button>
                </div>
              )}

              {/* Remove Button */}
              {!image.uploading && (
                <button
                  onClick={() => removeImage(index)}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                  aria-label="Remove image"
                >
                  ×
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Help Text */}
      <div className="text-xs text-gray-500">
        <p>• Supported formats: JPEG, PNG, WebP</p>
        <p>• Maximum file size: 5MB per image</p>
        <p>• Maximum {maxImages} images allowed</p>
      </div>
    </div>
  );
}
