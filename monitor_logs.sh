#!/bin/bash

echo "=== Sheepskin App Log Monitor ==="
echo "Press Ctrl+C to stop monitoring"
echo ""

# Function to show current status
show_status() {
    echo "=== PM2 Status ==="
    pm2 status
    echo ""
    
    echo "=== Last 10 Error Lines ==="
    pm2 logs sheepskin-app --err --lines 10
    echo ""
    
    echo "=== Last 5 Nginx Error Lines ==="
    sudo tail -5 /var/log/nginx/error.log
    echo ""
    echo "=== Starting Real-time Monitoring ==="
    echo "Press Ctrl+C to stop..."
    echo ""
}

# Show initial status
show_status

# Start real-time monitoring
pm2 logs sheepskin-app --lines 0
