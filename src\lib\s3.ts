import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Configure AWS S3 client
export const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  // Only use credentials if access keys are provided (for non-EC2 environments)
  ...(process.env.AWS_ACCESS_KEY_ID &&
    process.env.AWS_SECRET_ACCESS_KEY && {
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    }),
  // For EC2 with IAM role, credentials will be automatically detected
});

export const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME!;

/**
 * Generate a presigned URL for uploading files to S3
 */
export async function generatePresignedUploadUrl(
  key: string,
  contentType: string,
  expiresIn: number = 300 // 5 minutes
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: contentType,
  });

  return await getSignedUrl(s3Client, command, { expiresIn });
}

/**
 * Upload a file buffer directly to S3
 */
export async function uploadFileToS3(
  buffer: Buffer,
  key: string,
  contentType: string
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: buffer,
    ContentType: contentType,
  });

  await s3Client.send(command);

  // Return the proxy URL through our API instead of direct S3 URL
  // This allows us to serve images from a private S3 bucket with authentication
  const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
  return `${baseUrl}/api/images/${key}`;
}

/**
 * Delete a file from S3
 */
export async function deleteFileFromS3(key: string): Promise<void> {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  });

  await s3Client.send(command);
}

/**
 * Generate a unique file key for S3 storage
 */
export function generateFileKey(
  donationId: string,
  originalFileName: string,
  userId: string
): string {
  const timestamp = Date.now();
  const fileExtension = originalFileName.split(".").pop();
  return `donations/${userId}/${donationId}/${timestamp}.${fileExtension}`;
}

/**
 * Extract the S3 key from a full S3 URL
 */
export function extractS3KeyFromUrl(url: string): string {
  const urlParts = url.split("/");
  return urlParts.slice(3).join("/"); // Remove protocol, domain, and bucket parts
}

/**
 * Convert a direct S3 URL to a proxy URL through our API
 */
export function convertS3UrlToProxyUrl(s3Url: string): string {
  const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";

  // If it's already a proxy URL, return as is
  if (s3Url.includes("/api/images/")) {
    return s3Url;
  }

  // Extract the S3 key from the URL
  const s3Key = extractS3KeyFromUrl(s3Url);
  return `${baseUrl}/api/images/${s3Key}`;
}

/**
 * Validate file type for donation images
 */
export function validateImageFile(file: { mimetype: string; size: number }): {
  valid: boolean;
  error?: string;
} {
  const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
  const maxSize = 5 * 1024 * 1024; // 5MB

  if (!allowedTypes.includes(file.mimetype)) {
    return {
      valid: false,
      error: "Invalid file type. Only JPEG, PNG, and WebP images are allowed.",
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: "File size too large. Maximum size is 5MB.",
    };
  }

  return { valid: true };
}
