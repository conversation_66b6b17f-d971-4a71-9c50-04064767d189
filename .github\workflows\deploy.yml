name: CI/CD Pipeline for Sheepskin Collection Platform

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: "18"
  APP_NAME: "sheepskin-app"
  APP_DIRECTORY: "/home/<USER>/sheepskin-app"

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sheepskin_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Set up test environment
        run: |
          cat > .env.test << EOF
          DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET="test-secret-key-for-ci"
          NEXTAUTH_URL="http://localhost:3000"
          NODE_ENV="test"
          EOF

      - name: Generate Prisma client
        run: npx prisma generate

      - name: Run database migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Run TypeScript check
        run: npx tsc --noEmit

      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Build app
        run: npm run build
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET: "test-secret-key-for-ci"
          NEXTAUTH_URL: "http://localhost:3000"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: next-build
          path: |
            .next/
            package.json
            package-lock.json
            prisma/
            public/
          retention-days: 1

      - name: Run dependency security audit
        run: npm audit --audit-level moderate

  deploy:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: next-build

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Deploy to EC2
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
          PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          # Create temporary directory for SSH key
          mkdir -p ~/.ssh
          echo "$EC2_KEY" > ~/.ssh/ec2_key.pem
          chmod 600 ~/.ssh/ec2_key.pem

          # Create simplified deployment script
          cat > ~/deploy_script.sh << 'DEPLOY_EOF'
          #!/bin/bash
          set -e

          echo "Starting deployment of Sheepskin Collection Platform..."

          # Set environment variables (these will be substituted when the script is created)
          NEXTAUTH_SECRET_VAR="NEXTAUTH_SECRET_PLACEHOLDER"
          NEXTAUTH_URL_VAR="NEXTAUTH_URL_PLACEHOLDER"
          PAT_TOKEN_VAR="PAT_TOKEN_PLACEHOLDER"
          DB_PASSWORD="DB_PASSWORD_PLACEHOLDER"
          GITHUB_REPOSITORY="GITHUB_REPOSITORY_PLACEHOLDER"

          # Create app directory and clone repository
          cd /home/<USER>
          rm -rf sheepskin-app
          mkdir -p sheepskin-app
          cd sheepskin-app

          echo "Cloning repository: $GITHUB_REPOSITORY"
          git clone --branch main --single-branch https://$PAT_TOKEN_VAR:<EMAIL>/$GITHUB_REPOSITORY.git .
          echo "Repository cloned successfully. Latest commit:"
          git log --oneline -1

          # Copy existing .env file
          echo "Setting up environment variables..."
          if [ -f "/home/<USER>/.env" ]; then
            cp /home/<USER>/.env .env

            # Update environment variables for production
            sed -i '/^NEXTAUTH_SECRET=/d' .env 2>/dev/null || true
            sed -i '/^NEXTAUTH_URL=/d' .env 2>/dev/null || true
            sed -i '/^NODE_ENV=/d' .env 2>/dev/null || true

            echo "NEXTAUTH_SECRET=\"$NEXTAUTH_SECRET_VAR\"" >> .env
            echo "NEXTAUTH_URL=\"$NEXTAUTH_URL_VAR\"" >> .env
            echo "NODE_ENV=\"production\"" >> .env
          else
            echo "No existing .env file found"
            exit 1
          fi

          # Check if PostgreSQL Docker container is running
          echo "Checking PostgreSQL Docker container..."
          if ! sudo docker ps | grep -q sheepskin-db; then
            echo "PostgreSQL Docker container not running. Starting it..."
            # Try to start existing container or create new one
            sudo docker start sheepskin-db 2>/dev/null || \
            sudo docker run -d \
              --name sheepskin-db \
              -e POSTGRES_USER=postgres \
              -e POSTGRES_PASSWORD=Kingragnar1$ \
              -e POSTGRES_DB=sheepskin \
              -p 5432:5432 \
              postgres:15-alpine

            # Wait for PostgreSQL to be ready
            echo "Waiting for PostgreSQL to start..."
            sleep 10
          else
            echo "PostgreSQL Docker container is already running"
          fi

          # Verify DATABASE_URL is correctly set in .env
          if grep -q "DATABASE_URL" .env; then
            echo "DATABASE_URL found in .env file"
          else
            echo "Adding DATABASE_URL to .env file"
            echo "DATABASE_URL=\"postgresql://postgres:Kingragnar1\$@localhost:5432/sheepskin\"" >> .env
          fi

          # Test database connection
          echo "Testing database connection..."
          for i in {1..5}; do
            if PGPASSWORD="Kingragnar1\$" psql -h localhost -p 5432 -U postgres -d sheepskin -c "SELECT 1;" >/dev/null 2>&1; then
              echo "✅ Database connection successful"
              break
            fi
            if [ $i -eq 5 ]; then
              echo "❌ Database connection failed after 5 attempts"
              echo "Checking Docker PostgreSQL container status..."
              sudo docker ps | grep sheepskin-db || true
              echo "Checking Docker container logs..."
              sudo docker logs sheepskin-db --tail 20 || true
              echo "Checking if database exists..."
              sudo docker exec sheepskin-db psql -U postgres -l | grep sheepskin || true
              exit 1
            fi
            echo "Connection attempt $i failed, retrying..."
            sleep 3
          done

          # Stop the application first to ensure clean deployment
          echo "Stopping application..."
          pm2 stop sheepskin-app 2>/dev/null || echo "App not running"
          pm2 delete sheepskin-app 2>/dev/null || echo "App not in PM2"

          # Clean build artifacts
          echo "Cleaning build artifacts..."
          rm -rf .next node_modules

          # Install dependencies
          echo "Installing dependencies..."
          npm ci --omit=dev

          echo "Generating Prisma client..."
          npx prisma generate

          echo "Running database migrations..."
          npx prisma migrate deploy || npx prisma db push

          echo "Building application..."
          npm run build

          # Install PM2 and start application
          if ! command -v pm2 &> /dev/null; then
            sudo npm install -g pm2
          fi

          echo "Starting application with PM2..."
          pm2 start npm --name "sheepskin-app" -- start
          pm2 save



          echo "Waiting for application to start..."
          sleep 10
          pm2 status

          # Verify application is responding via nginx
          echo "Verifying application is accessible via nginx..."
          for i in {1..5}; do
            if curl -f http://localhost >/dev/null 2>&1; then
              echo "✅ Application is responding via nginx on port 80"
              break
            fi
            if [ $i -eq 5 ]; then
              echo "❌ Application not responding via nginx after 5 attempts"
              echo "PM2 logs:"
              pm2 logs sheepskin-app --lines 20
              echo "Nginx status:"
              sudo systemctl status nginx
              exit 1
            fi
            echo "Attempt $i failed, retrying..."
            sleep 3
          done

          echo "Deployment completed successfully!"
          DEPLOY_EOF

          # Replace placeholders with actual values
          sed -i "s|NEXTAUTH_SECRET_PLACEHOLDER|$NEXTAUTH_SECRET|g" ~/deploy_script.sh
          sed -i "s|NEXTAUTH_URL_PLACEHOLDER|$NEXTAUTH_URL|g" ~/deploy_script.sh
          sed -i "s|PAT_TOKEN_PLACEHOLDER|$PAT_TOKEN|g" ~/deploy_script.sh
          sed -i "s|DB_PASSWORD_PLACEHOLDER|$DB_PASSWORD|g" ~/deploy_script.sh
          sed -i "s|GITHUB_REPOSITORY_PLACEHOLDER|${{ github.repository }}|g" ~/deploy_script.sh

          # Execute deployment on EC2
          scp -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem ~/deploy_script.sh $EC2_USER@$EC2_HOST:/tmp/
          ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem $EC2_USER@$EC2_HOST "chmod +x /tmp/deploy_script.sh && /tmp/deploy_script.sh"
