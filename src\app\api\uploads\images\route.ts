import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { uploadFileToS3, validateImageFile, generateFileKey } from "@/lib/s3";

export async function POST(request: NextRequest) {
  try {
    console.log("🔵 Image upload request received");

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      console.log("❌ Unauthorized request - no session");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("✅ User authenticated:", session.user.email);

    // Parse the multipart form data
    const formData = await request.formData();
    const file = formData.get("image") as File;
    const donationId = formData.get("donationId") as string;

    console.log("📝 Form data received:", {
      hasFile: !!file,
      fileName: file?.name,
      fileSize: file?.size,
      donationId: donationId || "not provided",
    });

    if (!file) {
      console.log("❌ No file provided in request");
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // donationId is optional for temporary uploads
    // If not provided, use a temporary identifier
    console.log("🔄 Processing file upload...");

    // Validate the file
    const validation = validateImageFile({
      mimetype: file.type,
      size: file.size,
    });

    if (!validation.valid) {
      console.log("❌ File validation failed:", validation.error);
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    console.log("✅ File validation passed");

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());
    console.log("📦 File converted to buffer, size:", buffer.length);

    // Generate unique key for S3
    // Use donationId if provided, otherwise use a temporary identifier
    const tempId = donationId || `temp-${Date.now()}`;
    const fileKey = generateFileKey(tempId, file.name, session.user.id);
    console.log("🔑 Generated file key:", fileKey);

    // Upload to S3
    console.log("☁️ Uploading to S3...");
    const imageUrl = await uploadFileToS3(buffer, fileKey, file.type);
    console.log("✅ Upload successful, URL:", imageUrl);

    return NextResponse.json({
      success: true,
      imageUrl,
      message: "Image uploaded successfully",
    });
  } catch (error) {
    console.error("💥 Error uploading image:", error);
    console.error(
      "💥 Error stack:",
      error instanceof Error ? error.stack : "No stack trace"
    );
    return NextResponse.json(
      { error: "Internal server error during image upload" },
      { status: 500 }
    );
  }
}

// Configure the API route to handle multipart form data
export const config = {
  api: {
    bodyParser: false,
  },
};
